"use client";

import Image from "next/image";
import { redirect } from "next/navigation";
import { useSession } from "next-auth/react";

import type { Organization } from "@/app/services/organization.hooks";
import { OrgStatus } from "@/app/services/organization.hooks";

import { AppSpinner } from "../../components/app-spinner";
import { Body3, Body2 } from "../../components/app-typography";

const ORG_STATUS_MAP = {
  [OrgStatus.FREE]: "Recon Public Good",
  [OrgStatus.PAID]: "Recon PRO",
  [OrgStatus.TRIAL]: "Recon PRO Trial",
  [OrgStatus.NOORG]: "Please create an account!",
};

type AppUserProps = {
  organization: Organization;
  isLoading: boolean;
  orgStatus: OrgStatus;
};

export function AppUser({ organization, isLoading, orgStatus }: AppUserProps) {
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      redirect("/api/auth/signin?callbackUrl=/dashboard");
    },
  });

  if (!session || !session.user) return null;

  return (
    <div className="flex items-center gap-2">
      {status === "loading" && <AppSpinner />}

      <div className="h-6 w-px bg-stroke-neutral-decorative" />

      <div className="flex flex-col">
        <Body3 as="span" color="primary" className="w-[66px]">
          {session.user.name || "Username"}
        </Body3>

        {isLoading ? (
          <div className="h-4 w-16 animate-pulse rounded bg-back-neutral-tertiary" />
        ) : (
          <Body2 as="span" color="secondary" className="text-xs">
            {organization ? ORG_STATUS_MAP[orgStatus] : "No organization"}
          </Body2>
        )}
      </div>

      {session.user.image ? (
        <Image
          src={session.user.image}
          priority
          alt="Profile"
          width={32}
          height={32}
          className="rounded-full object-cover"
        />
      ) : (
        <div className="size-8 rounded-full bg-back-accent-primary" />
      )}
    </div>
  );
}
