import { useContext } from "react";

import { AppCode } from "@/app/components/app-code";

import { ABIContext } from "./abi-context";

export const ResultsPage = () => {
  const { allFuzzerContracts, reconSettingsFiles } = useContext(ABIContext);

  const reconSettingsCode = JSON.stringify(reconSettingsFiles, null, 2);

  return (
    <div className="mb-[30px] overflow-y-auto rounded-[19px] bg-aside px-[48px] py-[28px]">
      {allFuzzerContracts?.length > 0 ? (
        <>
          <p className="mb-[16px] border border-x-0 border-t-0 border-b-divider pb-[18px] text-textPrimary">
            Put the downloaded folder in /test/
          </p>
          {allFuzzerContracts.map((boilerplate) => (
            <div key={boilerplate.name}>
              <h2 className="mb-[17px] text-[21px] leading-[25px] text-textPrimary">
                {boilerplate.name}
              </h2>
              <h3 className="mb-[17px] text-textSecondary">
                {boilerplate.path}
              </h3>
              <AppCode code={boilerplate.data} language="javascript" />
            </div>
          ))}
        </>
      ) : (
        ""
      )}
      {!reconSettingsFiles ? (
        <AppCode code={reconSettingsCode} language="json" />
      ) : (
        ""
      )}
    </div>
  );
};
