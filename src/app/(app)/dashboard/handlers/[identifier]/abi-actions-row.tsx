import Link from "next/link";
import { useContext } from "react";
import { GoGear } from "react-icons/go";
import { IoMdDownload } from "react-icons/io";

import { AppButton } from "@/app/components/app-button";
import { ReconMode } from "@/app/services/generateFuzzerContracts";

import { ABIContext } from "./abi-context";

export const ABIActionsRow = () => {
  const { downloadAllContracts, setReconMode, reconMode } =
    useContext(ABIContext);

  const handleModeChange = (event) => {
    setReconMode(event.target.value);
  };

  return (
    <div className="mb-[21px] flex items-center justify-between">
      <div className="ml-auto flex gap-[27px]">
        <Link href="/installation-help">
          <AppButton
            className="w-[190px]"
            leftIcon={<GoGear />}
            variant="primary"
          >
            Installation Help
          </AppButton>
        </Link>
        <AppButton
          variant="primary"
          leftIcon={<IoMdDownload />}
          onClick={downloadAllContracts}
        >
          Download all files
        </AppButton>

        <select
          value={reconMode}
          onChange={handleModeChange}
          className="w-[210px] rounded-[10px] bg-[#171717] px-[14px] py-[9px] font-bold leading-[21px]  text-white"
        >
          <option value={ReconMode.normal}>Normal mode</option>
          <option value={ReconMode.fail}>Fail mode</option>
          <option value={ReconMode.catch}>Catch mode</option>
        </select>
      </div>
    </div>
  );
};
