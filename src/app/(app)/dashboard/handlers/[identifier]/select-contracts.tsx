import { useCallback, useContext } from "react";

import { AppRadioGroup } from "@/app/components/app-radio-group";
import {
  filterOutAllNonViewFunctions,
  processFunctions,
} from "@/app/services/generateFuzzerContracts";
import type { abiEntry } from "@/app/types/abi.api";

import { ABIContext, ShowMode } from "./abi-context";
import { ContractSelectionSection } from "./contract-selection-section";

const radioOptions = [
  { label: "Show all files", value: ShowMode.all },
  { label: "Show files from src only", value: ShowMode.src },
];

export const SelectContracts = () => {
  const { filteredAbiData, showMode, setShowMode, addContract } =
    useContext(ABIContext);

  const handleRadioChange = useCallback(
    (showMode: ShowMode) => {
      setShowMode(showMode);
    },
    [setShowMode]
  );

  return (
    <>
      <AppRadioGroup
        name="fileFilter"
        options={radioOptions}
        onChange={handleRadioChange}
        value={showMode}
      />
      <h2 className="my-[20px] text-[22px] leading-[26px] text-textPrimary">
        {filteredAbiData?.identifier}
      </h2>
      {filteredAbiData?.abiData?.map((singleContract: abiEntry) => {
        // addContract(singleContract.name); //@note Enable contracts by default
        return (
          <ContractSelectionSection
            key={singleContract.name}
            title={singleContract.name}
            functions={processFunctions(
              filterOutAllNonViewFunctions(singleContract.abi)
            )}
          />
        );
      })}
    </>
  );
};
