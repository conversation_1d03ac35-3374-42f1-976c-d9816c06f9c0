import type { ABIWithSelector } from "@recon-fuzz/abi-to-invariants-ts/src/types";
import { useTheme } from "next-themes";
import { useCallback, useContext, useMemo, useState } from "react";
import { FaSearch } from "react-icons/fa";
import {
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";

import { AppInput } from "@/app/components/app-input";
import { AppSwitch } from "@/app/components/app-switch";
import { cn } from "@/app/helpers/cn";
import { useBooleanState } from "@/app/services/helpers";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { ABIContext } from "./abi-context";

type ContractSelectionSectionProps = {
  title: string;
  type?: string;
  functions: ABIWithSelector[];
};

export const ContractSelectionSection = ({
  title,
  type,
  functions,
}: ContractSelectionSectionProps) => {
  const {
    skippedFunctions,
    addDisabledFunction,
    removeDisabledFunction,
    addContract,
    removeContract,
    isContractActive,
  } = useContext(ABIContext);
  const { theme } = useTheme();

  const isActive = isContractActive(title);

  const [isMinimized, { toggle }] = useBooleanState(true);
  const [search, setSearch] = useState("");

  const ChevronIcon = !isMinimized
    ? MdOutlineKeyboardArrowDown
    : MdOutlineKeyboardArrowUp;

  const onSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
    },
    []
  );

  const isFunctionDisabled = useCallback(
    (contractName: string, functionSelector: string) => {
      if (
        !skippedFunctions?.[contractName] ||
        !Array.isArray(skippedFunctions?.[contractName])
      ) {
        return false;
      }

      return skippedFunctions?.[contractName].includes(functionSelector);
    },
    [skippedFunctions]
  );

  const areAllFunctionsEnabled = useMemo(() => {
    return functions.every((f) => !isFunctionDisabled(title, f.selector));
  }, [isFunctionDisabled, functions, title]);

  const onActiveContractChange = useCallback(() => {
    if (!isActive) {
      addContract(title);
    } else {
      removeContract(title);
    }
  }, [addContract, removeContract, title, isActive]);

  const onEnableToggle = useCallback(() => {
    const mappedSelectors = functions.map((f) => f.selector);

    areAllFunctionsEnabled
      ? addDisabledFunction(title, mappedSelectors)
      : removeDisabledFunction(title, mappedSelectors);
  }, [
    removeDisabledFunction,
    areAllFunctionsEnabled,
    addDisabledFunction,
    functions,
    title,
  ]);

  const filteredFunctions = useMemo(() => {
    return functions.filter((f) =>
      f.name.toLowerCase().includes(search.toLowerCase())
    );
  }, [functions, search]);

  return (
    <div className="mt-[20px]">
      <div
        className={cn(
          "flex py-[20px] px-[22px] justify-between bg-blockBg gradient-dark-bg rounded-t-[8px] border-divider border border-b-0",
          {
            "rounded-b-[8px] border-b-1": isMinimized,
          }
        )}
      >
        <div>
          <p className="text-[18px] leading-[21px] text-textPrimary">{title}</p>
          <span className="text-[15px] font-thin leading-[18px] text-textSecondary">
            {type}
          </span>
        </div>
        <div className="flex gap-[20px]">
          <AppSwitch
            className="ml-auto mr-[16px]"
            onChange={onActiveContractChange}
            enabled={isActive}
          />
          <button
            className="flex size-[30px] items-center justify-center rounded-full bg-dashboardBG"
            onClick={toggle}
          >
            <ChevronIcon className="text-textPrimary" />
          </button>
        </div>
      </div>
      <div
        className={cn(
          "bg-aside mb-[17px]  overflow-hidden rounded-b-[8px] border border-divider border-t-0",
          {
            "h-0 py-0 border-none": isMinimized,
            "h-auto py-[20px]": !isMinimized,
            "bg-blockBG": theme === THEME_OPTIONS.light,
          }
        )}
      >
        <div className="flex items-center justify-between px-[28px]  pb-[20px] ">
          <AppInput
            value={search}
            onChange={onSearchChange}
            icon={FaSearch}
            disableError
            placeholder="Search"
          />
          <button
            className="bg-none text-[15px] leading-[18px] text-textPrimary underline"
            onClick={onEnableToggle}
          >
            {areAllFunctionsEnabled ? "Disable" : "Enable"} all
          </button>
        </div>
        <div className="max-h-[400px] overflow-y-auto  px-[28px] ">
          {filteredFunctions.map((f) => {
            const disabled = isFunctionDisabled(title, f.selector);
            return (
              <div
                key={f.name}
                className="flex items-center justify-between border-b border-divider py-[20px]"
              >
                <span className="text-[15px] leading-[18px] text-textPrimary">
                  {f.name}
                </span>
                <AppSwitch
                  onChange={() => {
                    disabled
                      ? removeDisabledFunction(title, f.selector)
                      : addDisabledFunction(title, f.selector);
                  }}
                  enabled={!disabled}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
