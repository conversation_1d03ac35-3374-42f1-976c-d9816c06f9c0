"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { THEME_OPTIONS, LOCAL_STORAGE_KEYS } from "./ThemeProvider";

export function usePersistedTheme() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [storedTheme, setStoredTheme] = useState<THEME_OPTIONS>(
    THEME_OPTIONS.dark
  );

  // Read from localStorage safely
  useEffect(() => {
    try {
      const stored = localStorage.getItem(LOCAL_STORAGE_KEYS.theme);
      if (stored) {
        // Handle both raw string and JSON string formats
        const parsedTheme = stored.startsWith('"')
          ? JSON.parse(stored)
          : stored;
        if (
          parsedTheme === THEME_OPTIONS.light ||
          parsedTheme === THEME_OPTIONS.dark
        ) {
          setStoredTheme(parsedTheme);
        }
      }
    } catch (error) {
      console.warn("Failed to read theme from localStorage:", error);
    }
  }, []);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && storedTheme && theme !== storedTheme) {
      setTheme(storedTheme);
    }
  }, [mounted, storedTheme, theme, setTheme]);

  useEffect(() => {
    if (mounted && theme && theme !== storedTheme) {
      const newTheme = theme as THEME_OPTIONS;
      setStoredTheme(newTheme);
      // Update localStorage manually to ensure compatibility with next-themes
      try {
        localStorage.setItem(
          LOCAL_STORAGE_KEYS.theme,
          JSON.stringify(newTheme)
        );
      } catch (error) {
        console.warn("Failed to save theme to localStorage:", error);
      }
    }
  }, [mounted, theme, storedTheme]);

  const handleThemeChange = (newTheme: THEME_OPTIONS) => {
    setTheme(newTheme);
    setStoredTheme(newTheme);
    // Update localStorage manually to ensure compatibility
    try {
      localStorage.setItem(LOCAL_STORAGE_KEYS.theme, JSON.stringify(newTheme));
    } catch (error) {
      console.warn("Failed to save theme to localStorage:", error);
    }
  };

  const currentTheme = mounted
    ? (theme as THEME_OPTIONS) || storedTheme
    : storedTheme;

  return {
    theme: currentTheme,
    setTheme: handleThemeChange,
    storedTheme,
    mounted,
    isDark: currentTheme === THEME_OPTIONS.dark,
  };
}
