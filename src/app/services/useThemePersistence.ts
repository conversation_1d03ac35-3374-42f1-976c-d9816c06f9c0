"use client";

import { useLocalStorage, useIsomorphicLayoutEffect } from "usehooks-ts";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { THEME_OPTIONS, LOCAL_STORAGE_KEYS } from "./ThemeProvider";

export function usePersistedTheme() {
  const { theme, setTheme } = useTheme();
  const [storedTheme, setStoredTheme] = useLocalStorage<THEME_OPTIONS>(
    LOCAL_STORAGE_KEYS.theme,
    THEME_OPTIONS.dark
  );
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useIsomorphicLayoutEffect(() => {
    if (mounted && storedTheme && theme !== storedTheme) {
      setTheme(storedTheme);
    }
  }, [mounted, storedTheme, theme, setTheme]);

  useEffect(() => {
    if (mounted && theme && theme !== storedTheme) {
      setStoredTheme(theme as THEME_OPTIONS);
    }
  }, [mounted, theme, storedTheme, setStoredTheme]);

  const handleThemeChange = (newTheme: THEME_OPTIONS) => {
    setTheme(newTheme);
    setStoredTheme(newTheme);
  };

  const currentTheme = mounted
    ? (theme as THEME_OPTIONS) || storedTheme
    : storedTheme;

  return {
    theme: currentTheme,
    setTheme: handleThemeChange,
    storedTheme,
    mounted,
    isDark: currentTheme === THEME_OPTIONS.dark,
  };
}
