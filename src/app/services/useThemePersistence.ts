"use client";

import { useLocalStorage, useIsomorphicLayoutEffect } from "usehooks-ts";
import { useTheme } from "next-themes";
import { useEffect } from "react";
import { THEME_OPTIONS, LOCAL_STORAGE_KEYS } from "./ThemeProvider";

export function usePersistedTheme() {
  const { theme, setTheme } = useTheme();
  const [storedTheme, setStoredTheme] = useLocalStorage<THEME_OPTIONS>(
    LOCAL_STORAGE_KEYS.theme,
    THEME_OPTIONS.dark
  );

  // Sync next-themes with localStorage on mount
  useIsomorphicLayoutEffect(() => {
    if (storedTheme && theme !== storedTheme) {
      setTheme(storedTheme);
    }
  }, []);

  // Update localStorage when theme changes
  useEffect(() => {
    if (theme && theme !== storedTheme) {
      setStoredTheme(theme as THEME_OPTIONS);
    }
  }, [theme, storedTheme, setStoredTheme]);

  const handleThemeChange = (newTheme: THEME_OPTIONS) => {
    setTheme(newTheme);
    setStoredTheme(newTheme);
  };

  return {
    theme: (theme as THEME_OPTIONS) || storedTheme,
    setTheme: handleThemeChange,
    storedTheme,
  };
}
