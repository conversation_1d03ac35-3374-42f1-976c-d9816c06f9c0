import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { PreInstallItem } from "./preinstall-item";
import { FORM_STYLES } from "./constants";

export const SubFormKontrol = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Target Test"
            {...register("kontrolTest")}
            type="text"
            placeholder="test_prove_*"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <PreInstallItem />
        </div>
      </div>
    </div>
  );
};
