import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { PreInstallItem } from "./preinstall-item";
import {
  FORM_STYLES,
  FOUNDRY_VERBOSITY_OPTIONS,
  FORK_MODE_OPTIONS,
  TEST_COMMAND_OPTIONS,
} from "./constants";

export const SubFormFoundry = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");
  const testCommand = watch("testCommand");

  useEffect(() => {}, [forkMode, testCommand, setValue]);

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Tester Contract Name"
            {...register("contract")}
            type="text"
            placeholder="InvariantTest"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Runs"
            {...register("runs")}
            type="text"
            placeholder="256"
          />
        </div>
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Seed"
            {...register("seed")}
            type="text"
            placeholder="Optional: Random seed"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppSelect
            className={FORM_STYLES.input}
            label="Select test command"
            {...register("testCommand")}
            options={TEST_COMMAND_OPTIONS}
          />
        </div>
      </div>

      {testCommand && testCommand !== "" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="Target Test"
              {...register("testTarget")}
              type="text"
              placeholder="test_invariant_*"
            />
          </div>
        </div>
      )}

      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppSelect
            className={FORM_STYLES.input}
            label="Select verbosity"
            {...register("verbosity")}
            options={FOUNDRY_VERBOSITY_OPTIONS}
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppSelect
            className={FORM_STYLES.input}
            label="Select Fork Mode"
            {...register("forkMode")}
            options={FORK_MODE_OPTIONS}
          />
        </div>
      </div>

      {forkMode && forkMode === "CUSTOM" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="RPC URL"
              {...register("rpcUrl")}
              type="text"
              placeholder="https://mainnet.infura.io/v3/..."
            />
          </div>
        </div>
      )}

      {forkMode && forkMode !== "NONE" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="Fork Block"
              {...register("forkBlock")}
              type="text"
              placeholder="LATEST"
            />
          </div>
        </div>
      )}

      <div className={FORM_STYLES.inputGroupSingle}>
        <div className={FORM_STYLES.fieldContainer}>
          <PreInstallItem />
        </div>
      </div>
    </div>
  );
};
