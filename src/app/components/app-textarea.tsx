import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";
import { useTheme } from "next-themes";

import { cn } from "../helpers/cn";
import { THEME_OPTIONS } from "../services/ThemeProvider";

type AppTextareaProps = {
  className?: string;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  rows?: number;
};

export const AppTextarea = forwardRef(
  (
    {
      onChange,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      rows = 10,
      ...rest
    }: AppTextareaProps,
    ref: any
  ) => {
    const id = useId();
    const { theme: currentTheme } = useTheme();
    const isDark = currentTheme === THEME_OPTIONS.dark;

    const getTextareaStyles = () => {
      const baseStyles =
        "min-h-[80px] rounded-[6px] px-4 py-3 w-full min-w-[200px] outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375] resize-vertical";

      if (disabled) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-white/60 text-white/60 opacity-40"
            : "bg-transparent border border-black/60 text-black/60 opacity-40"
        );
      }

      if (error) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-red-500 text-white/60 hover:border-red-400 focus:border-red-500"
            : "bg-transparent border border-red-500 text-black/60 hover:border-red-400 focus:border-red-500"
        );
      }

      return cn(
        baseStyles,
        isDark
          ? "bg-transparent border border-white/60 text-white/60 hover:border-white focus:border-white/60 placeholder:text-white/60"
          : "bg-transparent border border-black/60 text-black/60 hover:border-black focus:border-black/60 placeholder:text-black/60",
        {
          "pl-[40px]": !!Icon,
        }
      );
    };

    return (
      <div className={cn("relative w-full", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "mb-[3px] block text-[15px] leading-[18px]",
              isDark ? "text-white/80" : "text-black/80"
            )}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {!!Icon && (
            <Icon
              className={cn(
                "absolute left-3 top-3 size-4",
                isDark ? "text-white/60" : "text-black/60"
              )}
            />
          )}
          <textarea
            rows={rows}
            id={id}
            className={cn(getTextareaStyles(), className)}
            {...{
              placeholder,
              value,
              onChange,
              disabled,
              ref,
              ...rest,
            }}
          />
        </div>

        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-red-500">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppTextarea.displayName = "AppTextarea";
