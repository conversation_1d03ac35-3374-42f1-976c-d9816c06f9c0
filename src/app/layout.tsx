import "./globals.css";

import type { Metadata } from "next";

import { AppProviders } from "./app-providers";
import AuthProvider from "./context/AuthProvider";
import QueryProvider from "./services/QueryProvider";
import type { ReactNode } from "react";
import { blenderPro } from "./app.constants";

export const metadata: Metadata = {
  title: "Recon",
  description: "Recon helps you build and run invariant tests",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  var theme = localStorage.getItem('recon/theme/v1');
                  if (theme) {
                    document.documentElement.classList.add(theme === '"light"' ? 'light' : 'dark');
                  } else {
                    document.documentElement.classList.add('dark');
                  }
                } catch (e) {
                  document.documentElement.classList.add('dark');
                }
              })();
            `,
          }}
        />
      </head>
      <AuthProvider>
        <QueryProvider>
          <body className={blenderPro.className}>
            <AppProviders>{children}</AppProviders>
          </body>
        </QueryProvider>
      </AuthProvider>
    </html>
  );
}
